{"dependencies": {"@abstract-foundation/agw-client": "^1.5.0", "@mysten/sui.js": "^0.41.1", "@scure/bip39": "^1.2.1", "@solana/spl-token": "^0.4.14", "@solana/web3.js": "^1.98.4", "@supercharge/promise-pool": "^2.3.2", "axios": "^0.27.2", "bignumber.js": "^9.1.1", "chai": "^4.3.7", "dotenv": "^16.3.1", "ethereumjs-util": "^7.1.5", "ethers": "^5.6.9", "express": "^4.18.1", "firebase": "^9.9.3", "https-proxy-agent": "^7.0.6", "jsonwebtoken": "^8.5.1", "kafka-node": "^5.0.0", "lodash": "^4.17.21", "mocha": "^10.2.0", "moment": "^2.29.4", "node-telegram-bot-api": "^0.66.0", "p-retry": "^6.2.1", "socket.io-client": "^4.8.1", "tradingeconomics": "^2.2.4", "viem": "^2.23.9", "web3": "^4.9.0"}, "name": "example", "version": "1.0.0", "main": "elpis-nft.js", "scripts": {"test": "mocha"}, "keywords": [], "author": "", "license": "ISC", "description": ""}