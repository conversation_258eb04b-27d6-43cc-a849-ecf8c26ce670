const { Keypair } = require("@solana/web3.js");
const fs = require("fs");

/**
 * Generate Solana wallets and save them to a JSON file
 * @param {number} count - Number of wallets to generate
 * @param {string} filename - Output filename
 */
function generateSolanaWallets(count = 1000, filename = "./solana-wallets.json") {
  console.log(`Generating ${count} Solana wallets...`);
  
  const wallets = [];
  
  for (let i = 0; i < count; i++) {
    const keypair = Keypair.generate();
    const wallet = {
      address: keypair.publicKey.toString(),
      privateKey: Array.from(keypair.secretKey)
    };
    
    wallets.push(wallet);
    
    if ((i + 1) % 100 === 0) {
      console.log(`Generated ${i + 1}/${count} wallets...`);
    }
  }
  
  // Save to file
  fs.writeFileSync(filename, JSON.stringify(wallets, null, 2));
  console.log(`✓ Saved ${count} wallets to ${filename}`);
  
  // Display first few addresses for verification
  console.log("\nFirst 5 wallet addresses:");
  wallets.slice(0, 5).forEach((wallet, index) => {
    console.log(`  ${index}: ${wallet.address}`);
  });
}

/**
 * Convert existing private keys to the required format
 * @param {Array} privateKeys - Array of private keys (base58 strings or arrays)
 * @param {string} filename - Output filename
 */
function convertPrivateKeysToWallets(privateKeys, filename = "./solana-wallets.json") {
  console.log(`Converting ${privateKeys.length} private keys to wallet format...`);
  
  const wallets = privateKeys.map((privateKey, index) => {
    let keypair;
    
    try {
      if (Array.isArray(privateKey)) {
        keypair = Keypair.fromSecretKey(new Uint8Array(privateKey));
      } else if (typeof privateKey === 'string') {
        // Try base58 first
        try {
          const bs58 = require('bs58');
          keypair = Keypair.fromSecretKey(bs58.decode(privateKey));
        } catch {
          // If base58 fails, try as hex or JSON array
          const keyArray = privateKey.startsWith('0x') 
            ? Array.from(Buffer.from(privateKey.slice(2), 'hex'))
            : JSON.parse(privateKey);
          keypair = Keypair.fromSecretKey(new Uint8Array(keyArray));
        }
      }
      
      return {
        address: keypair.publicKey.toString(),
        privateKey: Array.from(keypair.secretKey)
      };
    } catch (error) {
      console.error(`Error converting private key ${index}: ${error.message}`);
      return null;
    }
  }).filter(wallet => wallet !== null);
  
  // Save to file
  fs.writeFileSync(filename, JSON.stringify(wallets, null, 2));
  console.log(`✓ Converted and saved ${wallets.length} wallets to ${filename}`);
}

// Example usage
if (require.main === module) {
  // Generate 1000 new wallets
  generateSolanaWallets(1000, "./solana-wallets.json");
  
  // Or convert existing private keys (uncomment and modify as needed)
  /*
  const existingPrivateKeys = [
    // Add your existing private keys here
    // Can be base58 strings, hex strings, or arrays
  ];
  convertPrivateKeysToWallets(existingPrivateKeys, "./solana-wallets.json");
  */
}

module.exports = {
  generateSolanaWallets,
  convertPrivateKeysToWallets
};
