// solTransfers.js
import fs from "fs";
import bs58 from "bs58";
import {
  Connection,
  clusterApiUrl,
  Keypair,
  LAMPORTS_PER_SOL,
  Transaction,
  SystemProgram,
  sendAndConfirmTransaction,
  PublicKey,
} from "@solana/web3.js";
import {
  getOrCreateAssociatedTokenAccount,
  createTransferCheckedInstruction,
} from "@solana/spl-token";

const connection = new Connection(clusterApiUrl("mainnet-beta"), "confirmed");
const MAIN_WALLET = "DkjL9fNcNXWfu1G1xDsctrn6WXXb3epyphKpyQurAhaU";

export function loadKeypair(pathOrBase58) {
  if (fs.existsSync(pathOrBase58)) {
    const raw = JSON.parse(fs.readFileSync(pathOrBase58, "utf8"));
    return Keypair.fromSecretKey(Uint8Array.from(raw));
  }
  try {
    const secret = bs58.decode(pathOrBase58);
    return Keypair.fromSecretKey(secret);
  } catch (e) {
    throw new Error(
      "Invalid keypair input. Provide path to JSON or base58 secret key."
    );
  }
}

export async function transferSol(fromKeypair, toPubkey, amountSol) {
  const toPub =
    typeof toPubkey === "string" ? new PublicKey(toPubkey) : toPubkey;
  const lamports = Math.round(amountSol * LAMPORTS_PER_SOL);

  const tx = new Transaction().add(
    SystemProgram.transfer({
      fromPubkey: fromKeypair.publicKey,
      toPubkey: toPub,
      lamports,
    })
  );

  const dummyTx = new Transaction().add(
    SystemProgram.transfer({
      fromPubkey: fromKeypair.publicKey,
      toPubkey: toPubkey,
      lamports: balanceLamports,
    })
  );

  const { value: { feeCalculator } } = await connection.getRecentBlockhashAndContext("confirmed");
  const estimatedFee = feeCalculator.lamportsPerSignature * dummyTx.signatures.length;


  const sig = await sendAndConfirmTransaction(connection, tx, [fromKeypair]);

  console.log("Transfer sol success");
  return sig; // transaction signature
}

export async function transferSPLToken(
  connection,
  payerKeypair,
  mintAddress,
  destinationOwner,
  amount,
  decimals,
  sourceTokenAccount = null
) {
  const mintPub =
    typeof mintAddress === "string" ? new PublicKey(mintAddress) : mintAddress;
  const destOwnerPub =
    typeof destinationOwner === "string"
      ? new PublicKey(destinationOwner)
      : destinationOwner;

  // Ensure destination associated token account exists (and get it)
  const destAta = await getOrCreateAssociatedTokenAccount(
    connection,
    payerKeypair, // payer
    mintPub, // mint
    destOwnerPub // owner of ATA
  );

  // Determine source ATA
  let sourceAtaInfo;
  if (sourceTokenAccount) {
    const srcPub =
      typeof sourceTokenAccount === "string"
        ? new PublicKey(sourceTokenAccount)
        : sourceTokenAccount;
    sourceAtaInfo = { address: srcPub };
  } else {
    // assume source is the associated token account of the payerKeypair.publicKey
    sourceAtaInfo = await getOrCreateAssociatedTokenAccount(
      connection,
      payerKeypair,
      mintPub,
      payerKeypair.publicKey
    );
  }

  // integer amount according to decimals
  const amountInteger = BigInt(Math.round(amount * Math.pow(10, decimals)));

  if (amountInteger <= 0n) throw new Error("Amount must be > 0");

  const ix = createTransferCheckedInstruction(
    sourceAtaInfo.address, // source token account
    mintPub, // mint
    destAta.address, // destination token account
    payerKeypair.publicKey, // owner of source token account
    amountInteger, // amount (as bigint or number)
    decimals // decimals of the mint
  );

  const tx = new Transaction().add(ix);
  const sig = await sendAndConfirmTransaction(connection, tx, [payerKeypair]);
  return sig;
}

export async function getSolBalance(publicKey) {
  try {
    const pubKey = new PublicKey(publicKey);

    const balanceLamports = await connection.getBalance(pubKey);
    return balanceLamports / LAMPORTS_PER_SOL;
  } catch (error) {
    console.error("Error fetching SOL balance:", error);
    throw error;
  }
}

const handlerTransferSol = async () => {
  const account = loadKeypair(
    "3uJvn9y3WEpGu8P4LHAbmoRXc737m1P6c6uPU9bEDnpycijff4C2HWwr8hff9dzwZ9ZoUuHL42BW4rQkD4poFXfx"
  );
  const solBalance = await getSolBalance(account.publicKey);
  const balanceDeductFee = solBalance - 0.0009;
  transferSol(account, MAIN_WALLET, balanceDeductFee);
};

handlerTransferSol();

// async function example() {
//   const from = loadKeypair("./my-keypair.json"); // or loadKeypair("BASE58_SECRET_KEY");

//   // 1) transfer 0.01 SOL to someone
//   const to = "TARGET_PUBLIC_KEY_HERE";
//   const solSig = await transferSol(connection, from, to, 0.01);

//   const mint = "TOKEN_MINT_ADDRESS_HERE";
//   const recipient = "RECIPIENT_PUBLIC_KEY_HERE";

//   const decimals = 6;
//   const tokenAmount = 1.25;

//   const tokenSig = await transferSPLToken(
//     connection,
//     from,
//     mint,
//     recipient,
//     tokenAmount,
//     decimals
//   );
// }
