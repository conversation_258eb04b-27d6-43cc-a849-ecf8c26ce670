// scan_similar.js
// Node.js (CommonJS). Usage:
// node scan_similar.js TARGET START_DATE END_DATE INTERVAL TOPK CANDIDATE_LIMIT
// Example:
// node scan_similar.js BTCUSDT 2025-07-01 2025-08-01 1h 10 200

const axios = require("axios");
const fs = require("fs");
const path = require("path");

const BINANCE = "https://api.binance.com";
const DEFAULT_CANDIDATE_LIMIT = 200;
const REQUEST_DELAY_MS = 120; // polite delay between requests to avoid rate-limit

/* ---------------------- helpers ---------------------- */
function sleep(ms) {
  return new Promise((r) => setTimeout(r, ms));
}

function toTimestampMs(v) {
  // accept ISO string or integer ms string
  if (!v) return null;
  if (/^\d+$/.test(String(v))) return Number(v);
  const t = Date.parse(v);
  if (isNaN(t)) throw new Error("Invalid date: " + v);
  return t;
}

function avg(arr) {
  if (!arr || arr.length === 0) return 0;
  return arr.reduce((a, b) => a + b, 0) / arr.length;
}
function std(arr) {
  if (!arr || arr.length === 0) return 0;
  const m = avg(arr);
  const v = avg(arr.map((x) => (x - m) ** 2));
  return Math.sqrt(v);
}
function intervalToMs(interval) {
  // common Binance intervals
  const n = parseInt(interval);
  if (interval.endsWith("m")) return n * 60 * 1000;
  if (interval.endsWith("h")) return n * 60 * 60 * 1000;
  if (interval.endsWith("d")) return n * 24 * 60 * 60 * 1000;
  if (interval.endsWith("w")) return n * 7 * 24 * 60 * 60 * 1000;
  if (interval.endsWith("M")) return n * 30 * 24 * 60 * 60 * 1000;
  throw new Error("Unsupported interval: " + interval);
}

/* ---------------------- Binance fetchers ---------------------- */

async function fetchExchangeInfo() {
  const url = BINANCE + "/api/v3/exchangeInfo";
  const res = await axios.get(url, { timeout: 10000 });
  return res.data;
}

async function fetch24hTickers() {
  const url = BINANCE + "/api/v3/ticker/24hr";
  const res = await axios.get(url, { timeout: 10000 });
  return res.data; // array
}

/**
 * Fetch klines for a symbol between startTime..endTime (ms).
 * Handles pagination (limit=1000 per request).
 * Returns array of candle objects { openTime, open, high, low, close, volume, closeTime }
 */
async function fetchKlines(symbol, interval, startTime, endTime) {
  const limit = 1000;
  const url = BINANCE + "/api/v3/klines";
  let results = [];
  let fetchStart = startTime;
  const step = intervalToMs(interval) * (limit - 1);

  while (fetchStart < endTime) {
    const params = {
      symbol,
      interval,
      startTime: fetchStart,
      endTime: endTime,
      limit,
    };
    try {
      const res = await axios.get(url, { params, timeout: 15000 });
      const data = res.data;
      if (!data || data.length === 0) break;
      const mapped = data.map((k) => ({
        openTime: k[0],
        open: parseFloat(k[1]),
        high: parseFloat(k[2]),
        low: parseFloat(k[3]),
        close: parseFloat(k[4]),
        volume: parseFloat(k[5]),
        closeTime: k[6],
      }));
      results = results.concat(mapped);
      // Binance returns up to 'limit' starting at fetchStart, set next fetchStart to last openTime + interval
      const lastOpen = mapped[mapped.length - 1].openTime;
      const next = lastOpen + intervalToMs(interval);
      if (next <= fetchStart) break; // safety
      fetchStart = next;
      if (data.length < limit) break;
      // small sleep to avoid hitting aggresssive rate limits if looping
      await sleep(80);
    } catch (err) {
      // on error, break and return what we have
      console.error("Error fetching klines for", symbol, err.message || err);
      break;
    }
  }

  // trim to be within startTime..endTime
  results = results.filter(
    (c) => c.openTime >= startTime && c.closeTime <= endTime
  );
  return results;
}

/* ---------------------- pattern detection ---------------------- */

function analyzeCandles(candles, opts = {}) {
  // opts thresholds
  const volSpikeStdMult = opts.volSpikeStdMult ?? 2;
  const bodySpikeStdMult = opts.bodySpikeStdMult ?? 2;
  const sidewayWindow = opts.sidewayWindow ?? 10;
  const sidewayStdRatio = opts.sidewayStdRatio ?? 0.02;

  if (!candles || candles.length === 0)
    return {
      volumeSpikes: [],
      candleSpikes: [],
      sidewayZones: [],
    };

  const closes = candles.map((c) => c.close);
  const volumes = candles.map((c) => c.volume);
  const volMean = avg(volumes),
    volStd = std(volumes);
  const volThreshold = volMean + volSpikeStdMult * volStd;
  const volumeSpikes = candles
    .map((c, i) => ({ ...c, idx: i }))
    .filter((c) => c.volume > volThreshold);

  const bodies = candles.map((c) => Math.abs(c.close - c.open));
  const bodyMean = avg(bodies),
    bodyStd = std(bodies);
  const bodyThreshold = bodyMean + bodySpikeStdMult * bodyStd;
  const candleSpikes = candles
    .map((c, i) => ({
      ...c,
      idx: i,
      body: Math.abs(c.close - c.open),
      direction: c.close > c.open ? "green" : "red",
    }))
    .filter((c) => c.body > bodyThreshold);

  // detect sideway zones: sliding window
  const sidewayZones = [];
  for (let i = 0; i + sidewayWindow <= closes.length; i++) {
    const win = closes.slice(i, i + sidewayWindow);
    const m = avg(win);
    const s = std(win);
    if (m > 0 && s / m < sidewayStdRatio) {
      sidewayZones.push({
        startIdx: i,
        endIdx: i + sidewayWindow - 1,
        startTime: candles[i].openTime,
        endTime: candles[i + sidewayWindow - 1].closeTime,
        mean: m,
        std: s,
        len: sidewayWindow,
      });
    }
  }

  // optionally merge adjacent/overlapping sideway windows into zones
  const merged = [];
  for (const z of sidewayZones) {
    if (merged.length === 0) {
      merged.push({ ...z });
    } else {
      const last = merged[merged.length - 1];
      if (z.startIdx <= last.endIdx + 1) {
        // merge
        last.endIdx = z.endIdx;
        last.endTime = z.endTime;
        last.len = last.endIdx - last.startIdx + 1;
        last.mean = (last.mean + z.mean) / 2;
        last.std = (last.std + z.std) / 2;
      } else {
        merged.push({ ...z });
      }
    }
  }

  return {
    volumeSpikes,
    candleSpikes,
    sidewayZones: merged,
  };
}

function extractFeaturesFromCandles(candles) {
  const det = analyzeCandles(candles);
  const nVolSpikes = det.volumeSpikes.length;
  const nCandleSpikes = det.candleSpikes.length;
  const nSideway = det.sidewayZones.length;
  const avgSidewayLen =
    nSideway > 0 ? avg(det.sidewayZones.map((z) => z.len)) : 0;
  // extras
  const pctGreen =
    candles.length > 0
      ? candles.filter((c) => c.close > c.open).length / candles.length
      : 0;
  const volatility = closesStdRelative(candles);
  return {
    nVolSpikes,
    nCandleSpikes,
    nSideway,
    avgSidewayLen,
    pctGreen,
    volatility,
  };
}

function closesStdRelative(candles) {
  if (!candles || candles.length === 0) return 0;
  const closes = candles.map((c) => c.close);
  const m = avg(closes);
  return m === 0 ? 0 : std(closes) / m;
}

/* ---------------------- similarity ---------------------- */

function featureDistance(f1, f2) {
  // keys we compare
  const keys = [
    "nVolSpikes",
    "nCandleSpikes",
    "nSideway",
    "avgSidewayLen",
    "pctGreen",
    "volatility",
  ];
  let sum = 0;
  for (const k of keys) {
    const a = f1[k] ?? 0;
    const b = f2[k] ?? 0;
    // normalized diff to avoid dominance of large numbers
    const denom = Math.abs(a) + Math.abs(b) + 1e-9;
    const diff = (a - b) / denom;
    sum += diff * diff;
  }
  return Math.sqrt(sum);
}

/* ---------------------- main pipeline ---------------------- */

async function getUsdtSymbolsSortedByVol(limit = DEFAULT_CANDIDATE_LIMIT) {
  const info = await fetchExchangeInfo();
  const tickers = await fetch24hTickers();
  // build map of 24h volume for symbol
  const volMap = {};
  for (const t of tickers) {
    volMap[t.symbol] = parseFloat(t.quoteVolume || t.volume || 0);
  }
  const symbols = info.symbols
    .filter(
      (s) =>
        s.status === "TRADING" &&
        s.quoteAsset === "USDT" &&
        s.isSpotTradingAllowed
    )
    .map((s) => s.symbol)
    .filter((sym) => sym.endsWith("USDT"));
  const ranked = symbols
    .map((sym) => ({ symbol: sym, vol: volMap[sym] || 0 }))
    .sort((a, b) => b.vol - a.vol)
    .slice(0, limit)
    .map((x) => x.symbol);
  return ranked;
}

async function findSimilar(
  targetSymbol,
  startMs,
  endMs,
  interval,
  topK = 10,
  candidateLimit = DEFAULT_CANDIDATE_LIMIT
) {
  console.log("Fetching target candles...", targetSymbol);
  const targetCandles = await fetchKlines(
    targetSymbol,
    interval,
    startMs,
    endMs
  );
  if (!targetCandles || targetCandles.length < 5) {
    throw new Error(
      "Not enough candle data for target " +
        targetSymbol +
        ". Got " +
        (targetCandles ? targetCandles.length : 0)
    );
  }
  const targetFeatures = extractFeaturesFromCandles(targetCandles);
  console.log("Target features:", targetFeatures);

  console.log(
    "Getting USDT symbols sorted by 24h volume (limit=" +
      candidateLimit +
      ") ..."
  );
  const symbols = await getUsdtSymbolsSortedByVol(candidateLimit);
  console.log("Symbols to scan:", symbols.length);

  const results = [];
  let idx = 0;
  for (const sym of symbols) {
    idx++;
    if (sym === targetSymbol) continue;
    try {
      process.stdout.write(
        `(${idx}/${symbols.length}) processing ${sym} ...\r`
      );
      const candles = await fetchKlines(sym, interval, startMs, endMs);
      if (!candles || candles.length < 10) {
        // skip
        await sleep(REQUEST_DELAY_MS);
        continue;
      }
      const feat = extractFeaturesFromCandles(candles);
      const dist = featureDistance(targetFeatures, feat);
      results.push({ symbol: sym, dist, feat });
    } catch (e) {
      // ignore
    }
    await sleep(REQUEST_DELAY_MS);
  }
  results.sort((a, b) => a.dist - b.dist);
  const out = results.slice(0, topK);
  return { targetFeatures, top: out, scanned: results.length };
}

/* ---------------------- CLI ---------------------- */

async function main() {
  const argv = process.argv.slice(2);
  if (argv.length < 4) {
    console.log(
      "Usage: node scan_similar.js TARGET START_DATE END_DATE INTERVAL TOPK CANDIDATE_LIMIT"
    );
    console.log(
      "Example: node scan_similar.js BTCUSDT 2025-07-01 2025-08-01 1h 10 200"
    );
    return;
  }
  const [target, startRaw, endRaw, interval] = argv;
  const topK = argv[4] ? parseInt(argv[4]) : 10;
  const candidateLimit = argv[5] ? parseInt(argv[5]) : DEFAULT_CANDIDATE_LIMIT;

  const startMs = toTimestampMs(startRaw);
  const endMs = toTimestampMs(endRaw);
  if (!startMs || !endMs || startMs >= endMs) {
    console.error("Invalid start/end");
    return;
  }

  console.log("Params:", {
    target,
    start: new Date(startMs).toISOString(),
    end: new Date(endMs).toISOString(),
    interval,
    topK,
    candidateLimit,
  });

  try {
    const res = await findSimilar(
      target,
      startMs,
      endMs,
      interval,
      topK,
      candidateLimit
    );
    console.log("\n--- Top similar results ---");
    res.top.forEach((r, i) => {
      console.log(
        `${i + 1}. ${r.symbol}  dist=${r.dist.toFixed(
          4
        )}  feat=${JSON.stringify(r.feat)}`
      );
    });
    console.log(`Scanned candidates: ${res.scanned}`);

    const outPath = path.join(
      process.cwd(),
      `similar_${target}_${Date.now()}.json`
    );
    fs.writeFileSync(
      outPath,
      JSON.stringify(
        {
          params: {
            target,
            start: startMs,
            end: endMs,
            interval,
            topK,
            candidateLimit,
          },
          result: res,
        },
        null,
        2
      )
    );
    console.log("Saved results to", outPath);
  } catch (err) {
    console.error("Error:", err && err.message ? err.message : err);
  }
}

if (require.main === module) {
  main();
}
