const { createAbstractClient } = require("@abstract-foundation/agw-client");
const { default: BigNumber } = require("bignumber.js");
const { isEmpty, random } = require("lodash");
const { http, parseAbi, parseEther } = require("viem");
const { privateKeyToAccount } = require("viem/accounts");
const { abstract } = require("viem/chains");
const { sleep } = require("../../helper");

const abi = [
  {
    inputs: [
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        internalType: "uint256",
        name: "nonce",
        type: "uint256",
      },
      {
        internalType: "bytes",
        name: "signature",
        type: "bytes",
      },
    ],
    name: "claimTokens",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
];

const claimReward = async (accessToken, id) => {
  await fetch("https://api.kap.gg/games/user/quests/claim_reward/", {
    headers: {
      accept: "*/*",
      "accept-language": "en-US,en;q=0.9",
      authorization: `Bearer ${accessToken}`,
      "content-type": "application/json",
      priority: "u=1, i",
      "sec-ch-ua":
        '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
      "sec-ch-ua-mobile": "?0",
      "sec-ch-ua-platform": '"Windows"',
      "sec-fetch-dest": "empty",
      "sec-fetch-mode": "cors",
      "sec-fetch-site": "same-site",
      Referer: "https://www.kap.gg/",
      "Referrer-Policy": "strict-origin-when-cross-origin",
    },
    body: JSON.stringify({ user_quest_id: id }),
    method: "POST",
  })
    .then(async function (response) {
      console.log("claimReward success", await response.json());
    })
    .catch(function (error) {
      console.log("claim error: " + error);
    });
};

const claimToken = async (accessToken, privateKey) => {
  await fetch("https://api.kap.gg/games/user/quests/claim_currency_reward/", {
    headers: {
      accept: "*/*",
      "accept-language": "en-US,en;q=0.9",
      authorization: `Bearer ${accessToken}`,
      "content-type": "application/json",
      priority: "u=1, i",
      "sec-ch-ua":
        '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
      "sec-ch-ua-mobile": "?0",
      "sec-ch-ua-platform": '"Windows"',
      "sec-fetch-dest": "empty",
      "sec-fetch-mode": "cors",
      "sec-fetch-site": "same-site",
    },
    referrer: "https://www.kap.gg/",
    referrerPolicy: "strict-origin-when-cross-origin",
    body: '{"currency":"mxp"}',
    method: "POST",
    mode: "cors",
    credentials: "include",
  }).then(async function (response) {
    const data = await response.json();
    console.log(data, "blockchain_data");

    claimOnchain(data.blockchain_data, privateKey);
  });
};

const claimOnchain = async (data, privateKey) => {
  if (isEmpty(data)) return;
  try {
    const account = privateKeyToAccount(privateKey);

    const agwClient = await createAbstractClient({
      chain: abstract,
      signer: account,
      transport: http(`https://api.mainnet.abs.xyz`),
    });

    const transactionHash = await agwClient.writeContract({
      abi: abi, // Your contract ABI
      address: "******************************************",
      functionName: "claimTokens",
      args: [parseEther(data.amount.toString()), data.nonce, data.signature],
    });

    console.log("claim success", transactionHash);
  } catch (error) {
    console.log("error", error);
  }
};

const init = async () => {
  const accounts = [
    {
      user_quest_id: 148364,
      privateKey:
        "0xc8779801273cb9e0a2501d863ee5ea5cbf89609cab9686224277b77426b30fc5",
      accessToken:
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************.aZ8a5AZtZNmqmEbWaU_FKX1F7FaYMPaLo4PY-KJ4_ws",
    },
    {
      user_quest_id: 157379, // EL
      privateKey:
        "0x6691ab469fef4762f0347666ceaaee48f9e67225c6042ae3c2278d5a09fc700e",
      accessToken:
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************.LuUo23_HSFkE4hx72M6kJhxrM701S9vfTiOCxaC29_g",
    },
    {
      user_quest_id: 154498, // tnhnam
      privateKey:
        "0xa9334cb60b03689778a46dcb549391e49e9bb25459b69113f5b53b800d11f1dc",
      accessToken:
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************.ZvK2FlMbP4hNu1zfWzyhdIJDSYt3_bdLG1BpzuJ4fy0",
    },
    {
      user_quest_id: 158006, // ghost gang
      privateKey:
        "0x804401dd3735c49652d85bedcc6be43265ca7dd4bc0520bbf859660a3de681b4",
      accessToken:
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************.yO1hT8DT4PqlBDs7KLyV47JOkUiBhsHUO14-ZpuM94U",
    },
    {
      user_quest_id: 423634, // su
      privateKey:
        "0x242faeeeab6ae60bc6059a1f934afb413844b9bb6ec890c9b2046bfac0d496f8",
      accessToken:
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************.Rp5RZ42Mgsl_hV_hEnb2a5q2HS9rFgtZ-cswsJW6vuc",
    },
  ];

  for (const account of accounts) {
    console.log("start account", account.privateKey);
    try {
      await claimReward(account.accessToken, account.user_quest_id || 0);
      await sleep(random(150000, 350000));
      await claimToken(account.accessToken, account.privateKey);
    } catch (err) {
      console.log(err, account.privateKey);
    }
  }
};
init();

setInterval(init, 1000 * 60 * 60 * 4);
