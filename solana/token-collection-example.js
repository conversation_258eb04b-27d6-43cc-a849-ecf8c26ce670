const { Keypair } = require("@solana/web3.js");
const { SolanaBatchTransfer } = require("./batch-transfer");

/**
 * Example script for token collection and SOL rotation
 * 
 * This script demonstrates how to:
 * 1. Collect leafToken from wallets that have tokens
 * 2. Rotate SOL to the next wallet in sequence
 */

async function runTokenCollection() {
  try {
    // Configuration
    const config = {
      rpcUrl: "https://api.mainnet-beta.solana.com", // or devnet: "https://api.devnet.solana.com"
      
      // Main wallet (used for fallback operations)
      mainWallet: Keypair.fromSecretKey(new Uint8Array([
        // Replace with your main wallet's private key array (64 bytes)
        // You can get this from your wallet's export function
        // Example: [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64]
      ])),
      
      // Token mint address for leafToken
      tokenMint: "YOUR_LEAF_TOKEN_MINT_ADDRESS", // Replace with actual leafToken mint address
      
      // Address where all tokens should be collected
      tokenCollectionAddress: "YOUR_COLLECTION_WALLET_ADDRESS", // Replace with your collection address
      
      // Path to your wallet file
      walletsFile: "./solana-wallets.json",
      
      // Batch processing settings
      batchSize: 5,    // Process 5 wallets at a time
      delayMs: 2000,   // Wait 2 seconds between batches
    };

    console.log("=== Token Collection and SOL Rotation ===");
    console.log(`Token mint: ${config.tokenMint}`);
    console.log(`Collection address: ${config.tokenCollectionAddress}`);
    console.log(`Wallet file: ${config.walletsFile}`);
    console.log(`Batch size: ${config.batchSize}`);
    
    // Create batch transfer instance
    const batchTransfer = new SolanaBatchTransfer(config);
    
    // Option 1: Process first 10 wallets (wallets 0-9)
    console.log("\n--- Processing first 10 wallets ---");
    await batchTransfer.executeTokenCollection(0, 10);
    
    // Option 2: Process specific range (uncomment to use)
    // console.log("\n--- Processing wallets 10-19 ---");
    // await batchTransfer.executeTokenCollection(10, 20);
    
    // Option 3: Process all wallets (uncomment to use)
    // console.log("\n--- Processing all wallets ---");
    // await batchTransfer.executeTokenCollection();
    
    console.log("\n✓ Token collection completed!");
    
  } catch (error) {
    console.error("Error during token collection:", error);
  }
}

/**
 * Check balances of specific wallets
 */
async function checkWalletBalances() {
  const config = {
    rpcUrl: "https://api.mainnet-beta.solana.com",
    mainWallet: Keypair.generate(), // Dummy wallet for initialization
    tokenMint: "YOUR_LEAF_TOKEN_MINT_ADDRESS",
  };
  
  const batchTransfer = new SolanaBatchTransfer(config);
  batchTransfer.loadWallets();
  
  console.log("=== Wallet Balance Check ===");
  
  // Check first 10 wallets
  for (let i = 0; i < Math.min(10, batchTransfer.wallets.length); i++) {
    const wallet = batchTransfer.wallets[i];
    const solBalance = await batchTransfer.getSOLBalance(wallet.address);
    const tokenBalance = await batchTransfer.getTokenBalance(wallet.address);
    
    console.log(`Wallet ${i}: ${wallet.address}`);
    console.log(`  SOL: ${solBalance} SOL`);
    console.log(`  Tokens: ${tokenBalance}`);
    console.log("");
  }
}

/**
 * Find wallets with tokens
 */
async function findWalletsWithTokens() {
  const config = {
    rpcUrl: "https://api.mainnet-beta.solana.com",
    mainWallet: Keypair.generate(), // Dummy wallet for initialization
    tokenMint: "YOUR_LEAF_TOKEN_MINT_ADDRESS",
  };
  
  const batchTransfer = new SolanaBatchTransfer(config);
  batchTransfer.loadWallets();
  
  console.log("=== Finding Wallets with Tokens ===");
  
  const walletsWithTokens = [];
  
  for (let i = 0; i < batchTransfer.wallets.length; i++) {
    const wallet = batchTransfer.wallets[i];
    const tokenBalance = await batchTransfer.getTokenBalance(wallet.address);
    
    if (tokenBalance > 0) {
      walletsWithTokens.push({
        index: i,
        address: wallet.address,
        tokenBalance: tokenBalance
      });
      console.log(`Wallet ${i}: ${wallet.address} - ${tokenBalance} tokens`);
    }
    
    // Add delay to avoid rate limiting
    if (i % 10 === 0) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
  
  console.log(`\nFound ${walletsWithTokens.length} wallets with tokens`);
  return walletsWithTokens;
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'collect';
  
  switch (command) {
    case 'collect':
      await runTokenCollection();
      break;
    case 'check':
      await checkWalletBalances();
      break;
    case 'find':
      await findWalletsWithTokens();
      break;
    default:
      console.log("Usage:");
      console.log("  node token-collection-example.js collect  - Run token collection");
      console.log("  node token-collection-example.js check    - Check wallet balances");
      console.log("  node token-collection-example.js find     - Find wallets with tokens");
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  runTokenCollection,
  checkWalletBalances,
  findWalletsWithTokens
};
