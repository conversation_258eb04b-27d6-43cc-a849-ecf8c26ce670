// solTransfers.js
import fs from "fs";
import bs58 from "bs58";
import {
  Connection,
  clusterApiUrl,
  Keypair,
  LAMPORTS_PER_SOL,
  Transaction,
  SystemProgram,
  sendAndConfirmTransaction,
  PublicKey,
} from "@solana/web3.js";
import {
  getOrCreateAssociatedTokenAccount,
  createTransferCheckedInstruction,
  getAccount,
} from "@solana/spl-token";

const connection = new Connection(clusterApiUrl("mainnet-beta"), "confirmed");
const MAIN_WALLET = "DkjL9fNcNXWfu1G1xDsctrn6WXXb3epyphKpyQurAhaU";
const LEAF_ADDRESS = "EWbYEzhuyNm8pZntv1bbHUQtsJCW1esErofEUSyYpump";
const DECIMALS = 6;

export function loadKeypair(pathOrBase58) {
  if (fs.existsSync(pathOrBase58)) {
    const raw = JSON.parse(fs.readFileSync(pathOrBase58, "utf8"));
    return Keypair.fromSecretKey(Uint8Array.from(raw));
  }
  try {
    const secret = bs58.decode(pathOrBase58);
    return Keypair.fromSecretKey(secret);
  } catch (e) {
    throw new Error(
      "Invalid keypair input. Provide path to JSON or base58 secret key."
    );
  }
}

export async function transferSol(fromKeypair, toPubkey, amountSol) {
  const toPub =
    typeof toPubkey === "string" ? new PublicKey(toPubkey) : toPubkey;
  const lamports = Math.round(amountSol * LAMPORTS_PER_SOL);

  const tx = new Transaction({
    feePayer: fromKeypair.publicKey,
  }).add(
    SystemProgram.transfer({
      fromPubkey: fromKeypair.publicKey,
      toPubkey: toPub,
      lamports,
    })
  );

  const { blockhash } = await connection.getLatestBlockhash("confirmed");
  tx.recentBlockhash = blockhash;

  const estimatedFee = await connection.getFeeForMessage(
    tx.compileMessage(),
    "confirmed"
  );

  const finaltx = new Transaction({
    feePayer: fromKeypair.publicKey,
  }).add(
    SystemProgram.transfer({
      fromPubkey: fromKeypair.publicKey,
      toPubkey: toPub,
      lamports: lamports - estimatedFee.value,
    })
  );

  const sig = await sendAndConfirmTransaction(connection, finaltx, [
    fromKeypair,
  ]);

  console.log(`Transfer sol success from ${fromKeypair.publicKey} to ${toPub}`);
  return sig; // transaction signature
}

export async function transferSPLToken(fromKeypair) {
  const fromPubkey = fromKeypair.publicKey;
  const mintPubkey = new PublicKey(LEAF_ADDRESS);
  const destPubkey = new PublicKey(MAIN_WALLET);

  const { amount, tokenAccount } = await getSPLTokenBalance(fromPubkey);
  const destTokenAccount = await getOrCreateAssociatedTokenAccount(
    connection,
    fromKeypair,
    mintPubkey,
    destPubkey
  );

  const ix = createTransferCheckedInstruction(
    tokenAccount, // Source token account
    mintPubkey, // Mint
    destTokenAccount.address, // Destination token account
    fromPubkey, // Owner of source token account
    amount, // Amount in smallest unit
    DECIMALS // Decimals
  );

  const tx = new Transaction().add(ix);
  const sig = await sendAndConfirmTransaction(connection, tx, [fromKeypair]);
  console.log(`transfer token of ${amount} from ${fromPubkey}  success`);
  return sig;
}

export async function getSolBalance(publicKey) {
  try {
    const pubKey = new PublicKey(publicKey);
    const balanceLamports = await connection.getBalance(pubKey);
    return balanceLamports / LAMPORTS_PER_SOL;
  } catch (error) {
    console.error("Error fetching SOL balance:", error);
    throw error;
  }
}

export async function getSPLTokenBalance(walletAddress) {
  const walletPubkey = new PublicKey(walletAddress);
  const mintPubkey = new PublicKey(LEAF_ADDRESS);

  const tokenAccount = await getOrCreateAssociatedTokenAccount(
    connection,
    walletPubkey,
    mintPubkey,
    walletPubkey
  );
  const accountInfo = await getAccount(connection, tokenAccount.address);

  console.log(`Balance of ${walletAddress} is ${accountInfo.amount}`);

  return { amount: accountInfo.amount, tokenAccount };
}

const handlerTransferSol = async () => {
  const account = loadKeypair(
    "3uJvn9y3WEpGu8P4LHAbmoRXc737m1P6c6uPU9bEDnpycijff4C2HWwr8hff9dzwZ9ZoUuHL42BW4rQkD4poFXfx"
  );

  transferSPLToken(account);
  // const solBalance = await getSolBalance(account.publicKey);
  // transferSol(account, MAIN_WALLET, solBalance);
};

handlerTransferSol();

// async function example() {
//   const from = loadKeypair("./my-keypair.json"); // or loadKeypair("BASE58_SECRET_KEY");

//   // 1) transfer 0.01 SOL to someone
//   const to = "TARGET_PUBLIC_KEY_HERE";
//   const solSig = await transferSol(connection, from, to, 0.01);

//   const mint = "TOKEN_MINT_ADDRESS_HERE";
//   const recipient = "RECIPIENT_PUBLIC_KEY_HERE";

//   const decimals = 6;
//   const tokenAmount = 1.25;

//   const tokenSig = await transferSPLToken(
//     connection,
//     from,
//     mint,
//     recipient,
//     tokenAmount,
//     decimals
//   );
// }
